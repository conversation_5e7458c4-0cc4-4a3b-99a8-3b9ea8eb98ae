# Cached Function - Go Implementation

A high-performance, thread-safe caching library for Go that provides memoization, in-flight request deduplication, TTL expiration, and LRU eviction for expensive function calls.

## Features

- **Memoization**: Cache function results to avoid redundant computations
- **In-flight Request Deduplication**: Multiple concurrent requests with the same parameters execute only once
- **TTL Expiration**: Cached results expire after 5 minutes (configurable)
- **LRU Eviction**: Automatic eviction of least recently used entries when cache reaches capacity
- **Thread-Safe**: Concurrent-safe operations using Go's synchronization primitives
- **Generic Support**: Type-safe implementation using Go generics
- **Production Ready**: Comprehensive error handling, logging, and observability

## Installation

```bash
go mod init your-project
go get github.com/cache/cached-function
```

## Quick Start

```go
package main

import (
    "fmt"
    "time"
    "github.com/cache/cached-function/pkg/cachedfunction"
)

// A long-running function that fetches data
func fetchDataFromRemote(id int) (string, error) {
    time.Sleep(2 * time.Second) // Simulate expensive operation
    return fmt.Sprintf("Result for ID %d", id), nil
}

func main() {
    // Wrap the function with caching
    cachedFetch := cachedfunction.NewCachedFunction(fetchDataFromRemote)
    
    // First call - executes the function
    result, err := cachedFetch(42)
    if err != nil {
        panic(err)
    }
    fmt.Println(result) // Takes ~2 seconds
    
    // Second call - returns from cache instantly
    result, err = cachedFetch(42)
    fmt.Println(result) // Returns immediately
}
```

## Advanced Usage

### Custom Configuration

```go
// Create cached function with custom capacity and TTL
cachedFn := cachedfunction.NewCachedFunctionWithConfig(
    expensiveFunction,
    500,              // capacity: 500 entries
    10*time.Minute,   // TTL: 10 minutes
)
```

### Concurrent Deduplication

```go
var wg sync.WaitGroup
cachedFetch := cachedfunction.NewCachedFunction(fetchDataFromRemote)

// Launch 10 goroutines calling the same function
for i := 0; i < 10; i++ {
    wg.Add(1)
    go func() {
        defer wg.Done()
        result, err := cachedFetch(100)
        if err == nil {
            fmt.Println("Got:", result)
        }
    }()
}
wg.Wait()
// Only one actual function execution occurs
```

### Error Handling

```go
func mayFailFunction(id int) (string, error) {
    if id < 0 {
        return "", fmt.Errorf("invalid ID: %d", id)
    }
    return fmt.Sprintf("Success: %d", id), nil
}

cachedFn := cachedfunction.NewCachedFunction(mayFailFunction)

// Errors are also cached
result, err := cachedFn(-1) // Executes function, caches error
result, err = cachedFn(-1)  // Returns cached error instantly
```

## Architecture

### Core Components

1. **CachedFunction**: Generic wrapper that provides the public API
2. **Cache**: Thread-safe cache implementation with TTL and LRU
3. **LRU**: Doubly-linked list implementation for O(1) eviction
4. **Entry**: Cache entry with value, error, and metadata
5. **InFlightManager**: Handles concurrent request deduplication

### Design Patterns

- **Clean Architecture**: Separation of concerns with clear boundaries
- **Generic Programming**: Type-safe API using Go 1.18+ generics
- **Observer Pattern**: TTL and LRU tracking with automatic cleanup
- **Singleton Pattern**: In-flight request deduplication per key

## Performance

### Benchmarks

Run benchmarks to compare performance:

```bash
go test -bench=. -benchmem ./test/
```

Expected results:
- **Cache Hit**: ~100ns per operation
- **Cache Miss**: Function execution time + ~1μs overhead
- **Concurrent Deduplication**: Near-linear scaling with goroutines

### Memory Usage

- **Per Entry**: ~200 bytes (including metadata and pointers)
- **LRU Overhead**: ~48 bytes per entry (doubly-linked list)
- **Concurrency Overhead**: ~100 bytes per in-flight request

## Testing

### Run Unit Tests

```bash
go test ./test/ -v
```

### Run Benchmarks

```bash
go test -bench=. -benchmem ./test/
```

### Test Coverage

```bash
go test -cover ./test/
```

## Configuration

### Default Settings

- **Capacity**: 1000 entries
- **TTL**: 5 minutes
- **Eviction Policy**: LRU (Least Recently Used)

### Customization

```go
// Custom configuration
cachedFn := cachedfunction.NewCachedFunctionWithConfig(
    yourFunction,
    2000,             // Custom capacity
    15*time.Minute,   // Custom TTL
)
```

## Thread Safety

All operations are thread-safe and can be called concurrently from multiple goroutines:

- **Read Operations**: Use RWMutex for optimal read performance
- **Write Operations**: Properly synchronized with mutex locks
- **In-flight Requests**: Managed with sync.Map for lock-free operations

## Limitations

1. **Key Generation**: Uses `fmt.Sprintf("%v", param)` for cache keys
2. **Single Parameter**: Current API supports single-parameter functions only
3. **Memory Bounds**: No automatic memory pressure handling
4. **Serialization**: Complex types may have expensive key generation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Examples

See `cmd/example/main.go` for comprehensive usage examples including:
- Basic caching
- Concurrent deduplication
- Error handling
- Different parameter types
