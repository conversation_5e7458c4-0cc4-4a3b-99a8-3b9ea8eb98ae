package test

import (
	"fmt"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/cache/cached-function/pkg/cachedfunction"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test function that tracks how many times it's called
func createTestFunction() (func(int) (string, error), *int64) {
	callCount := int64(0)
	fn := func(id int) (string, error) {
		atomic.AddInt64(&callCount, 1)
		time.Sleep(100 * time.Millisecond) // Simulate work
		if id < 0 {
			return "", fmt.Errorf("negative ID: %d", id)
		}
		return fmt.Sprintf("result-%d", id), nil
	}
	return fn, &callCount
}

func TestBasicCaching(t *testing.T) {
	testFn, callCount := createTestFunction()
	cachedFn := cachedfunction.NewCachedFunction(testFn)

	// First call should execute the function
	result1, err1 := cachedFn(42)
	require.NoError(t, err1)
	assert.Equal(t, "result-42", result1)
	assert.Equal(t, int64(1), atomic.LoadInt64(callCount))

	// Second call should return cached result
	result2, err2 := cachedFn(42)
	require.NoError(t, err2)
	assert.Equal(t, "result-42", result2)
	assert.Equal(t, int64(1), atomic.LoadInt64(callCount)) // Should still be 1

	// Different parameter should execute function again
	result3, err3 := cachedFn(43)
	require.NoError(t, err3)
	assert.Equal(t, "result-43", result3)
	assert.Equal(t, int64(2), atomic.LoadInt64(callCount))
}

func TestErrorCaching(t *testing.T) {
	testFn, callCount := createTestFunction()
	cachedFn := cachedfunction.NewCachedFunction(testFn)

	// First call with error
	result1, err1 := cachedFn(-1)
	require.Error(t, err1)
	assert.Contains(t, err1.Error(), "negative ID")
	assert.Equal(t, "", result1)
	assert.Equal(t, int64(1), atomic.LoadInt64(callCount))

	// Second call should return cached error
	result2, err2 := cachedFn(-1)
	require.Error(t, err2)
	assert.Contains(t, err2.Error(), "negative ID")
	assert.Equal(t, "", result2)
	assert.Equal(t, int64(1), atomic.LoadInt64(callCount)) // Should still be 1
}

func TestConcurrentDeduplication(t *testing.T) {
	testFn, callCount := createTestFunction()
	cachedFn := cachedfunction.NewCachedFunction(testFn)

	const numGoroutines = 10
	var wg sync.WaitGroup
	results := make([]string, numGoroutines)
	errors := make([]error, numGoroutines)

	// Launch multiple goroutines calling the same function simultaneously
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			result, err := cachedFn(999)
			results[index] = result
			errors[index] = err
		}(i)
	}

	wg.Wait()

	// All calls should succeed with the same result
	for i := 0; i < numGoroutines; i++ {
		require.NoError(t, errors[i])
		assert.Equal(t, "result-999", results[i])
	}

	// Function should have been called only once
	assert.Equal(t, int64(1), atomic.LoadInt64(callCount))
}

func TestTTLExpiration(t *testing.T) {
	testFn, callCount := createTestFunction()
	// Create cached function with very short TTL for testing
	cachedFn := cachedfunction.NewCachedFunctionWithConfig(testFn, 1000, 200*time.Millisecond)

	// First call
	result1, err1 := cachedFn(100)
	require.NoError(t, err1)
	assert.Equal(t, "result-100", result1)
	assert.Equal(t, int64(1), atomic.LoadInt64(callCount))

	// Second call within TTL should be cached
	result2, err2 := cachedFn(100)
	require.NoError(t, err2)
	assert.Equal(t, "result-100", result2)
	assert.Equal(t, int64(1), atomic.LoadInt64(callCount))

	// Wait for TTL to expire
	time.Sleep(300 * time.Millisecond)

	// Third call after TTL should execute function again
	result3, err3 := cachedFn(100)
	require.NoError(t, err3)
	assert.Equal(t, "result-100", result3)
	assert.Equal(t, int64(2), atomic.LoadInt64(callCount))
}

func TestCacheCapacityAndLRU(t *testing.T) {
	testFn, callCount := createTestFunction()
	// Create cached function with small capacity for testing
	cachedFn := cachedfunction.NewCachedFunctionWithConfig(testFn, 3, 5*time.Minute)

	// Fill cache to capacity
	for i := 1; i <= 3; i++ {
		result, err := cachedFn(i)
		require.NoError(t, err)
		assert.Equal(t, fmt.Sprintf("result-%d", i), result)
	}
	assert.Equal(t, int64(3), atomic.LoadInt64(callCount))

	// Access first entry to make it recently used
	result, err := cachedFn(1)
	require.NoError(t, err)
	assert.Equal(t, "result-1", result)
	assert.Equal(t, int64(3), atomic.LoadInt64(callCount)) // Should still be 3 (cached)

	// Add new entry, should evict least recently used (entry 2)
	result, err = cachedFn(4)
	require.NoError(t, err)
	assert.Equal(t, "result-4", result)
	assert.Equal(t, int64(4), atomic.LoadInt64(callCount))

	// Entry 1 should still be cached (was accessed recently)
	result, err = cachedFn(1)
	require.NoError(t, err)
	assert.Equal(t, "result-1", result)
	assert.Equal(t, int64(4), atomic.LoadInt64(callCount)) // Should still be 4

	// Entry 2 should have been evicted and need recomputation
	result, err = cachedFn(2)
	require.NoError(t, err)
	assert.Equal(t, "result-2", result)
	assert.Equal(t, int64(5), atomic.LoadInt64(callCount)) // Should be 5 now
}
