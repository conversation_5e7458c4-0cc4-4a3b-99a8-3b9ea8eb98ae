package test

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/cache/cached-function/pkg/cachedfunction"
)

// Benchmark function that simulates expensive computation
func expensiveFunction(id int) (string, error) {
	time.Sleep(10 * time.Millisecond) // Simulate expensive operation
	return fmt.Sprintf("computed-result-%d", id), nil
}

// Fast function for comparison
func fastFunction(id int) (string, error) {
	return fmt.Sprintf("fast-result-%d", id), nil
}

func BenchmarkDirectFunctionCall(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = expensiveFunction(i % 100) // Use modulo to have some repeated values
	}
}

func BenchmarkCachedFunctionColdCache(b *testing.B) {
	cachedFn := cachedfunction.NewCachedFunction(expensiveFunction)
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _ = cachedFn(i) // Each call is unique, so cache is always cold
	}
}

func BenchmarkCachedFunctionWarmCache(b *testing.B) {
	cachedFn := cachedfunction.NewCachedFunction(expensiveFunction)

	// Pre-warm the cache
	for i := 0; i < 100; i++ {
		_, _ = cachedFn(i)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = cachedFn(i % 100) // All calls should hit the cache
	}
}

func BenchmarkCachedFunctionMixedCache(b *testing.B) {
	cachedFn := cachedfunction.NewCachedFunction(expensiveFunction)
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, _ = cachedFn(i % 100) // 50% cache hits after first 100 calls
	}
}

func BenchmarkHighConcurrencyDirect(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			_, _ = expensiveFunction(i % 10)
			i++
		}
	})
}

func BenchmarkHighConcurrencyCached(b *testing.B) {
	cachedFn := cachedfunction.NewCachedFunction(expensiveFunction)

	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			_, _ = cachedFn(i % 10) // High cache hit rate
			i++
		}
	})
}

func BenchmarkConcurrentDeduplication(b *testing.B) {
	cachedFn := cachedfunction.NewCachedFunction(expensiveFunction)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, _ = cachedFn(1) // All goroutines call with same parameter
		}
	})
}

func BenchmarkCacheOverhead(b *testing.B) {
	// Benchmark with a very fast function to measure cache overhead
	fastCachedFn := cachedfunction.NewCachedFunction(fastFunction)

	// Pre-warm cache
	for i := 0; i < 100; i++ {
		_, _ = fastCachedFn(i)
	}

	b.Run("Direct", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, _ = fastFunction(i % 100)
		}
	})

	b.Run("Cached", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, _ = fastCachedFn(i % 100)
		}
	})
}

// Benchmark to test performance under different cache sizes
func BenchmarkCacheSizes(b *testing.B) {
	sizes := []int{10, 100, 1000, 10000}

	for _, size := range sizes {
		b.Run(fmt.Sprintf("Size%d", size), func(b *testing.B) {
			cachedFn := cachedfunction.NewCachedFunctionWithConfig(
				expensiveFunction,
				size,
				5*time.Minute,
			)

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_, _ = cachedFn(i % (size * 2)) // 50% cache hit rate
			}
		})
	}
}

// Stress test with many concurrent goroutines
func BenchmarkStressTest(b *testing.B) {
	cachedFn := cachedfunction.NewCachedFunction(expensiveFunction)

	b.ResetTimer()

	var wg sync.WaitGroup
	numGoroutines := 100
	callsPerGoroutine := b.N / numGoroutines

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			for j := 0; j < callsPerGoroutine; j++ {
				// Mix of unique and repeated calls
				key := (goroutineID*1000 + j) % 50
				_, _ = cachedFn(key)
			}
		}(i)
	}

	wg.Wait()
}
