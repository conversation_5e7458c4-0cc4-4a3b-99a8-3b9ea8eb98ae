package cache

import (
	"log"
	"sync"
	"time"
)

const (
	DefaultTTL      = 5 * time.Minute
	DefaultCapacity = 1000
)

// InFlightRequest represents a request that's currently being processed
type InFlightRequest struct {
	result<PERSON>han chan *Entry
	done       chan struct{}
}

// Cache implements a thread-safe cache with TTL and LRU eviction
type Cache struct {
	mu       sync.RWMutex
	entries  map[string]*Entry
	lru      *LRU
	capacity int
	ttl      time.Duration
	inFlight sync.Map // map[string]*InFlightRequest
}

// NewCache creates a new cache with the specified capacity and TTL
func NewCache(capacity int, ttl time.Duration) *Cache {
	if capacity <= 0 {
		capacity = DefaultCapacity
	}
	if ttl <= 0 {
		ttl = DefaultTTL
	}

	return &Cache{
		entries:  make(map[string]*Entry),
		lru:      NewLRU(),
		capacity: capacity,
		ttl:      ttl,
	}
}

// Get retrieves a value from the cache
func (c *Cache) Get(key string) (interface{}, error, bool) {
	c.mu.RLock()
	entry, exists := c.entries[key]
	c.mu.RUnlock()

	if !exists {
		return nil, nil, false
	}

	// Check if expired
	if entry.IsExpired() {
		c.Delete(key)
		return nil, nil, false
	}

	// Update LRU order
	c.mu.Lock()
	entry.Touch()
	c.lru.MoveToFront(entry)
	c.mu.Unlock()

	return entry.Value, entry.Error, true
}

// Set stores a value in the cache
func (c *Cache) Set(key string, value interface{}, err error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// Check if entry already exists
	if existingEntry, exists := c.entries[key]; exists {
		// Update existing entry
		existingEntry.Value = value
		existingEntry.Error = err
		existingEntry.ExpiresAt = time.Now().Add(c.ttl)
		existingEntry.Touch()
		c.lru.MoveToFront(existingEntry)
		return
	}

	// Create new entry
	entry := NewEntry(key, value, err, c.ttl)

	// Check capacity and evict if necessary
	if len(c.entries) >= c.capacity {
		c.evictLRU()
	}

	// Add to cache
	c.entries[key] = entry
	c.lru.AddToFront(entry)

	log.Printf("Cache: Added entry for key %s", key)
}

// Delete removes an entry from the cache
func (c *Cache) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if entry, exists := c.entries[key]; exists {
		delete(c.entries, key)
		c.lru.Remove(entry)
		log.Printf("Cache: Deleted entry for key %s", key)
	}
}

// evictLRU removes the least recently used entry (must be called with lock held)
func (c *Cache) evictLRU() {
	if c.lru.Size() == 0 {
		return
	}

	lruEntry := c.lru.RemoveLast()
	if lruEntry != nil {
		delete(c.entries, lruEntry.Key)
		log.Printf("Cache: Evicted LRU entry for key %s", lruEntry.Key)
	}
}

// Size returns the current number of entries in the cache
func (c *Cache) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return len(c.entries)
}

// GetOrCompute retrieves a value from cache or computes it if not present
// This method handles in-flight request deduplication
func (c *Cache) GetOrCompute(key string, computeFn func() (interface{}, error)) (interface{}, error) {
	// First, try to get from cache
	if value, err, found := c.Get(key); found {
		return value, err
	}

	// Check if there's already an in-flight request for this key
	if existingReq, loaded := c.inFlight.Load(key); loaded {
		// Another goroutine is already computing this value, wait for it
		req := existingReq.(*InFlightRequest)
		select {
		case result := <-req.resultChan:
			return result.Value, result.Error
		case <-req.done:
			// The computing goroutine finished, try to get from cache again
			if value, err, found := c.Get(key); found {
				return value, err
			}
			// If still not found, fall through to compute ourselves
		}
	}

	// Try to become the computing goroutine
	req := &InFlightRequest{
		resultChan: make(chan *Entry, 1),
		done:       make(chan struct{}),
	}

	if actualReq, loaded := c.inFlight.LoadOrStore(key, req); loaded {
		// Someone else became the computing goroutine, wait for them
		existingReq := actualReq.(*InFlightRequest)
		select {
		case result := <-existingReq.resultChan:
			return result.Value, result.Error
		case <-existingReq.done:
			// Try cache again
			if value, err, found := c.Get(key); found {
				return value, err
			}
			// Fallback: compute ourselves (this shouldn't happen often)
		}
	}

	// We are the computing goroutine
	defer func() {
		// Notify all waiting goroutines
		close(req.done)
		// Clean up in-flight request
		c.inFlight.Delete(key)
	}()

	// Compute the value
	value, err := computeFn()

	// Store in cache
	c.Set(key, value, err)

	// Create result entry and send to waiting goroutines
	result := &Entry{
		Value: value,
		Error: err,
	}

	// Send result to all waiting goroutines (non-blocking)
	select {
	case req.resultChan <- result:
	default:
		// Channel is full or no one is waiting, that's fine
	}

	return value, err
}

// Clear removes all entries from the cache
func (c *Cache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.entries = make(map[string]*Entry)
	c.lru = NewLRU()
	log.Println("Cache: Cleared all entries")
}
