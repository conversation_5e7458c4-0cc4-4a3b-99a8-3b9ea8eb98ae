package cache

// LRU implements a Least Recently Used eviction policy using a doubly-linked list
type LRU struct {
	head *Entry
	tail *Entry
	size int
}

// NewLRU creates a new LRU list
func NewLRU() *LRU {
	// Create dummy head and tail nodes to simplify insertion/deletion
	head := &Entry{}
	tail := &Entry{}
	head.next = tail
	tail.prev = head

	return &LRU{
		head: head,
		tail: tail,
		size: 0,
	}
}

// MoveToFront moves an entry to the front of the list (most recently used)
func (lru *LRU) MoveToFront(entry *Entry) {
	// Remove from current position
	lru.remove(entry)
	// Add to front
	lru.addToFront(entry)
}

// AddToFront adds a new entry to the front of the list
func (lru *LRU) AddToFront(entry *Entry) {
	lru.addToFront(entry)
	lru.size++
}

// RemoveLast removes and returns the least recently used entry
func (lru *LRU) RemoveLast() *Entry {
	if lru.size == 0 {
		return nil
	}

	last := lru.tail.prev
	lru.remove(last)
	lru.size--
	return last
}

// Size returns the current size of the LRU list
func (lru *LRU) Size() int {
	return lru.size
}

// remove removes an entry from the list (internal helper)
func (lru *LRU) remove(entry *Entry) {
	if entry.prev != nil {
		entry.prev.next = entry.next
	}
	if entry.next != nil {
		entry.next.prev = entry.prev
	}
}

// addToFront adds an entry to the front of the list (internal helper)
func (lru *LRU) addToFront(entry *Entry) {
	entry.next = lru.head.next
	entry.prev = lru.head
	lru.head.next.prev = entry
	lru.head.next = entry
}

// Remove removes an entry from the list and decrements size
func (lru *LRU) Remove(entry *Entry) {
	lru.remove(entry)
	lru.size--
}
