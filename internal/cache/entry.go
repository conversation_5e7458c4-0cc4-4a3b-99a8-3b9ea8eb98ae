package cache

import (
	"time"
)

// Entry represents a cached value with expiration and LRU tracking
type Entry struct {
	Key        string
	Value      interface{}
	Error      error
	ExpiresAt  time.Time
	AccessedAt time.Time

	// LRU linked list pointers
	prev *Entry
	next *Entry
}

// IsExpired checks if the cache entry has expired
func (e *Entry) IsExpired() bool {
	return time.Now().After(e.ExpiresAt)
}

// Touch updates the access time for LRU tracking
func (e *Entry) Touch() {
	e.AccessedAt = time.Now()
}

// NewEntry creates a new cache entry with TTL
func NewEntry(key string, value interface{}, err error, ttl time.Duration) *Entry {
	now := time.Now()
	return &Entry{
		Key:        key,
		Value:      value,
		Error:      err,
		ExpiresAt:  now.Add(ttl),
		AccessedAt: now,
	}
}
