package main

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/cache/cached-function/pkg/cachedfunction"
)

// A long-running function that fetches data (simulated with sleep)
func fetchDataFromRemote(id int) (string, error) {
	fmt.Printf("Executing fetchDataFromRemote for ID %d\n", id)
	time.Sleep(2 * time.Second)
	return fmt.Sprintf("Result for ID %d", id), nil
}

// A function that might return an error
func fetchDataWithError(id int) (string, error) {
	fmt.Printf("Executing fetchDataWithError for ID %d\n", id)
	time.Sleep(1 * time.Second)

	if id < 0 {
		return "", fmt.Errorf("invalid ID: %d", id)
	}

	return fmt.Sprintf("Success result for ID %d", id), nil
}

func main() {
	fmt.Println("=== Cached Function Demo ===")

	// Example 1: Basic caching
	fmt.Println("\n1. Basic Caching Example:")
	cachedFetch := cachedfunction.NewCachedFunction(fetchDataFromRemote)

	start := time.Now()
	result, err := cachedFetch(42)
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("First call result: %s (took %v)\n", result, time.Since(start))

	// This second call will return instantly from the cache
	start = time.Now()
	result, err = cachedFetch(42)
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("Second call result: %s (took %v)\n", result, time.Since(start))

	// Example 2: Concurrent deduplication
	fmt.Println("\n2. Concurrent Deduplication Example:")
	var wg sync.WaitGroup
	cachedFetchConcurrent := cachedfunction.NewCachedFunction(fetchDataFromRemote)

	start = time.Now()
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			result, err := cachedFetchConcurrent(100)
			if err == nil {
				fmt.Printf("Goroutine %d got: %s\n", goroutineID, result)
			} else {
				fmt.Printf("Goroutine %d error: %v\n", goroutineID, err)
			}
		}(i)
	}
	wg.Wait()
	fmt.Printf("All concurrent calls completed in %v\n", time.Since(start))
	fmt.Println("Note: Only one fetchDataFromRemote should have been executed")

	// Example 3: Error handling
	fmt.Println("\n3. Error Handling Example:")
	cachedFetchWithError := cachedfunction.NewCachedFunction(fetchDataWithError)

	// Valid call
	result, err = cachedFetchWithError(5)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		fmt.Printf("Success: %s\n", result)
	}

	// Invalid call (will be cached too)
	result, err = cachedFetchWithError(-1)
	if err != nil {
		fmt.Printf("Expected error: %v\n", err)
	} else {
		fmt.Printf("Unexpected success: %s\n", result)
	}

	// Second invalid call (should return cached error instantly)
	start = time.Now()
	result, err = cachedFetchWithError(-1)
	if err != nil {
		fmt.Printf("Cached error: %v (took %v)\n", err, time.Since(start))
	}

	// Example 4: Different parameter types
	fmt.Println("\n4. Different Parameter Types Example:")
	stringFunction := func(s string) (int, error) {
		fmt.Printf("Processing string: %s\n", s)
		time.Sleep(500 * time.Millisecond)
		return len(s), nil
	}

	cachedStringFunc := cachedfunction.NewCachedFunction(stringFunction)

	result1, _ := cachedStringFunc("hello")
	fmt.Printf("Length of 'hello': %d\n", result1)

	result2, _ := cachedStringFunc("world")
	fmt.Printf("Length of 'world': %d\n", result2)

	// Cached call
	start = time.Now()
	result3, _ := cachedStringFunc("hello")
	fmt.Printf("Cached length of 'hello': %d (took %v)\n", result3, time.Since(start))

	fmt.Println("\n=== Demo Complete ===")
}
