.PHONY: help build test benchmark clean run example lint fmt vet deps

# Default target
help:
	@echo "Available targets:"
	@echo "  build      - Build the project"
	@echo "  test       - Run unit tests"
	@echo "  benchmark  - Run benchmark tests"
	@echo "  example    - Run the example application"
	@echo "  lint       - Run linter"
	@echo "  fmt        - Format code"
	@echo "  vet        - Run go vet"
	@echo "  deps       - Download dependencies"
	@echo "  clean      - Clean build artifacts"
	@echo "  coverage   - Run tests with coverage"

# Build the project
build:
	@echo "Building project..."
	go build -v ./...

# Run unit tests
test:
	@echo "Running unit tests..."
	go test -v ./test/

# Run benchmark tests
benchmark:
	@echo "Running benchmarks..."
	go test -bench=. -benchmem ./test/

# Run the example application
example:
	@echo "Running example..."
	go run cmd/example/main.go

# Run linter (requires golangci-lint)
lint:
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed. Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Run go vet
vet:
	@echo "Running go vet..."
	go vet ./...

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	go mod download
	go mod tidy

# Clean build artifacts
clean:
	@echo "Cleaning..."
	go clean ./...
	rm -f coverage.out

# Run tests with coverage
coverage:
	@echo "Running tests with coverage..."
	go test -coverprofile=coverage.out ./test/
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run all checks (format, vet, lint, test)
check: fmt vet lint test
	@echo "All checks passed!"

# Install development tools
install-tools:
	@echo "Installing development tools..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Quick development cycle
dev: fmt vet test
	@echo "Development cycle complete!"
