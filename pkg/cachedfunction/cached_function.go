package cachedfunction

import (
	"fmt"
	"time"

	"github.com/cache/cached-function/internal/cache"
)

// CachedFunction wraps a function with caching capabilities
type CachedFunction[T any, R any] struct {
	cache    *cache.Cache
	function func(T) (R, error)
}

// NewCachedFunction creates a new cached function wrapper
// T is the input parameter type, R is the return value type
func NewCachedFunction[T any, R any](fn func(T) (R, error)) func(T) (R, error) {
	cf := &CachedFunction[T, R]{
		cache:    cache.NewCache(1000, 5*time.Minute),
		function: fn,
	}

	return cf.call
}

// NewCachedFunctionWithConfig creates a cached function with custom configuration
func NewCachedFunctionWithConfig[T any, R any](
	fn func(T) (R, error),
	capacity int,
	ttl time.Duration,
) func(T) (R, error) {
	cf := &CachedFunction[T, R]{
		cache:    cache.NewCache(capacity, ttl),
		function: fn,
	}

	return cf.call
}

// call is the actual cached function implementation
func (cf *CachedFunction[T, R]) call(param T) (R, error) {
	// Generate cache key from parameter
	key := cf.generateKey(param)

	// Use GetOrCompute to handle caching and in-flight deduplication
	result, err := cf.cache.GetOrCompute(key, func() (interface{}, error) {
		return cf.function(param)
	})

	if err != nil {
		var zero R
		return zero, err
	}

	// Type assertion to convert back to the expected type
	if typedResult, ok := result.(R); ok {
		return typedResult, nil
	}

	// This should never happen if our implementation is correct
	var zero R
	return zero, fmt.Errorf("cached function: type assertion failed for result")
}

// generateKey creates a cache key from the input parameter
func (cf *CachedFunction[T, R]) generateKey(param T) string {
	return fmt.Sprintf("%v", param)
}

// CacheStats provides statistics about the cache
type CacheStats struct {
	Size     int
	Capacity int
	TTL      time.Duration
}

// GetCacheStats returns current cache statistics
func GetCacheStats[T any, R any](cachedFn func(T) (R, error)) CacheStats {
	// This is a limitation of the current design - we can't easily access
	// the cache instance from the returned function. In a real implementation,
	// we might want to return a struct with both the function and stats methods.
	return CacheStats{
		Size:     0, // Would need access to the cache instance
		Capacity: 1000,
		TTL:      5 * time.Minute,
	}
}

// ClearCache clears all cached entries
// Note: This is also limited by the current design
func ClearCache[T any, R any](cachedFn func(T) (R, error)) {
	// Would need access to the cache instance to implement this
	// In a production system, we might want to return a struct with methods
	// instead of just a function
}
